<div class="space-y-6">
    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert flex rounded-lg border border-success bg-success/10 py-4 px-4 text-success sm:px-5">
            <svg xmlns="http://www.w3.org/2000/svg" class="size-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <div class="ml-2">
                <h3 class="text-sm font-medium">{{ session('message') }}</h3>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert flex rounded-lg border border-error bg-error/10 py-4 px-4 text-error sm:px-5">
            <svg xmlns="http://www.w3.org/2000/svg" class="size-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            <div class="ml-2">
                <h3 class="text-sm font-medium">{{ session('error') }}</h3>
            </div>
        </div>
    @endif

    <!-- WhatsApp Web Connection Status -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Connection Status Card -->
        <div class="p-6 border border-slate-200 rounded-lg dark:border-navy-500 {{ $isConnected ? 'bg-success/5 border-success/20' : 'bg-slate-50 dark:bg-navy-800' }}">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                    <div class="avatar size-12">
                        <div class="is-initial rounded-full bg-green-500 text-white">
                            <i class="fab fa-whatsapp text-xl"></i>
                        </div>
                    </div>
                    <div>
                        <h4 class="font-medium text-lg">WhatsApp Web</h4>
                        <p class="text-sm {{ $isConnected ? 'text-success' : 'text-slate-400 dark:text-navy-300' }}">
                            {{ $connectionStatus }}
                        </p>
                    </div>
                </div>
                @if($isConnected)
                    <div class="badge bg-success/10 text-success dark:bg-success/15">
                        <div class="size-2 rounded-full bg-success mr-1"></div>
                        Connected
                    </div>
                @else
                    <div class="badge bg-slate-200 text-slate-600 dark:bg-navy-500 dark:text-navy-100">
                        <div class="size-2 rounded-full bg-slate-400 mr-1"></div>
                        Disconnected
                    </div>
                @endif
            </div>

            <!-- Connection Info -->
            @if($isConnected && $connectedPhone)
                <div class="space-y-2 p-3 bg-white/50 dark:bg-navy-700/50 rounded-lg">
                    <div class="flex justify-between text-sm">
                        <span class="text-slate-600 dark:text-navy-300">Phone Number:</span>
                        <span class="font-medium">{{ $connectedPhone }}</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-slate-600 dark:text-navy-300">Connected Since:</span>
                        <span class="font-medium">{{ $connectedSince }}</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-slate-600 dark:text-navy-300">Session Status:</span>
                        <span class="font-medium text-success">Active</span>
                    </div>
                </div>
            @endif
        </div>

        <!-- QR Code Card -->
        <div class="p-6 border border-slate-200 rounded-lg dark:border-navy-500 bg-white dark:bg-navy-800">
            <h4 class="font-medium text-lg mb-4">Connection Setup</h4>

            @if(!$isConnected)
                <div class="text-center">
                    @if($qrCode)
                        <div class="mb-4">
                            <div class="inline-block p-4 bg-white rounded-lg shadow-sm">
                                <img src="data:image/png;base64,{{ $qrCode }}" alt="WhatsApp QR Code" class="w-48 h-48 mx-auto">
                            </div>
                        </div>
                        <p class="text-sm text-slate-600 dark:text-navy-300 mb-4">
                            Scan this QR code with your WhatsApp mobile app to connect
                        </p>
                        <div class="flex justify-center space-x-2">
                            <button wire:click="refreshQR"
                                class="btn border border-slate-300 text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                                <i class="fas fa-sync-alt mr-2"></i>
                                Refresh QR
                            </button>
                        </div>
                    @else
                        <div class="py-12">
                            <div class="w-48 h-48 mx-auto bg-slate-100 dark:bg-navy-600 rounded-lg flex items-center justify-center mb-4">
                                @if($isGeneratingQR)
                                    <div class="text-center">
                                        <div class="spinner size-8 animate-spin border-2 border-primary border-r-transparent rounded-full mb-2"></div>
                                        <p class="text-sm text-slate-600 dark:text-navy-300">Generating QR Code...</p>
                                    </div>
                                @else
                                    <div class="text-center">
                                        <i class="fas fa-qrcode text-4xl text-slate-400 dark:text-navy-300 mb-2"></i>
                                        <p class="text-sm text-slate-600 dark:text-navy-300">QR Code will appear here</p>
                                    </div>
                                @endif
                            </div>
                            <button wire:click="generateQR"
                                class="btn bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90"
                                {{ $isGeneratingQR ? 'disabled' : '' }}>
                                <i class="fas fa-qrcode mr-2"></i>
                                Generate QR Code
                            </button>
                        </div>
                    @endif
                </div>
            @else
                <div class="text-center py-12">
                    <div class="w-48 h-48 mx-auto bg-success/10 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-check-circle text-6xl text-success"></i>
                    </div>
                    <h5 class="font-medium text-success mb-2">Successfully Connected!</h5>
                    <p class="text-sm text-slate-600 dark:text-navy-300">Your WhatsApp Web session is active and ready to use.</p>
                </div>
            @endif
        </div>
    </div>

    <!-- WhatsApp Web Configuration -->
    <div class="space-y-4">
        <h4 class="font-medium text-slate-700 dark:text-navy-100">WhatsApp Web Configuration</h4>

        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <label class="block">
                <span>Session Name</span>
                <input wire:model="sessionName"
                    class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                    placeholder="My WhatsApp Session" type="text" />
                @error('sessionName') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>

            <label class="block">
                <span>Webhook URL</span>
                <input wire:model="webhookUrl"
                    class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                    placeholder="https://yoursite.com/webhook/whatsapp" type="url" />
                @error('webhookUrl') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>
        </div>

        <div class="space-y-2">
            <label class="inline-flex items-center space-x-2">
                <input wire:model="saveSession"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Save Session Data</span>
            </label>
            <p class="text-xs text-slate-500 dark:text-navy-400 ml-12">Keep session active between server restarts</p>

            <label class="inline-flex items-center space-x-2">
                <input wire:model="enableLogs"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Enable Debug Logs</span>
            </label>
            <p class="text-xs text-slate-500 dark:text-navy-400 ml-12">Log WhatsApp Web events for debugging</p>
        </div>
    </div>

    <!-- Auto Reply Settings -->
    <div class="space-y-4">
        <h4 class="font-medium text-slate-700 dark:text-navy-100">Auto Reply Settings</h4>

        <label class="inline-flex items-center space-x-2">
            <input wire:model="autoReply"
                class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                type="checkbox" />
            <span>Enable Auto Reply</span>
        </label>

        @if($autoReply)
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <label class="block">
                    <span>Welcome Message <span class="text-error">*</span></span>
                    <textarea wire:model="welcomeMessage"
                        class="form-textarea mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                        rows="3" placeholder="Enter welcome message"></textarea>
                    @error('welcomeMessage') <span class="text-xs text-error">{{ $message }}</span> @enderror
                </label>

                <label class="block">
                    <span>Away Message <span class="text-error">*</span></span>
                    <textarea wire:model="awayMessage"
                        class="form-textarea mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                        rows="3" placeholder="Enter away message"></textarea>
                    @error('awayMessage') <span class="text-xs text-error">{{ $message }}</span> @enderror
                </label>
            </div>
        @endif
    </div>

    <!-- Business Hours -->
    <div class="space-y-4">
        <label class="inline-flex items-center space-x-2">
            <input wire:model="businessHours"
                class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                type="checkbox" />
            <span>Enable Business Hours</span>
        </label>

        @if($businessHours)
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <label class="block">
                    <span>Start Time <span class="text-error">*</span></span>
                    <input wire:model="startTime"
                        class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                        type="time" />
                    @error('startTime') <span class="text-xs text-error">{{ $message }}</span> @enderror
                </label>

                <label class="block">
                    <span>End Time <span class="text-error">*</span></span>
                    <input wire:model="endTime"
                        class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                        type="time" />
                    @error('endTime') <span class="text-xs text-error">{{ $message }}</span> @enderror
                </label>
            </div>
        @endif
    </div>

    <!-- Advanced Settings -->
    <div class="space-y-4">
        <h4 class="font-medium text-slate-700 dark:text-navy-100">Advanced Settings</h4>

        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <label class="block">
                <span>Message Delay (seconds)</span>
                <input wire:model="messageDelay"
                    class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                    type="number" min="0" max="60" placeholder="2" />
                @error('messageDelay') <span class="text-xs text-error">{{ $message }}</span> @enderror
                <p class="text-xs text-slate-500 dark:text-navy-400 mt-1">Delay between sending messages to avoid spam detection</p>
            </label>

            <label class="block">
                <span>Max Messages Per Hour</span>
                <input wire:model="maxMessagesPerHour"
                    class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                    type="number" min="1" max="100" placeholder="30" />
                @error('maxMessagesPerHour') <span class="text-xs text-error">{{ $message }}</span> @enderror
                <p class="text-xs text-slate-500 dark:text-navy-400 mt-1">Rate limit to prevent account restrictions</p>
            </label>
        </div>

        <div class="space-y-2">
            <label class="inline-flex items-center space-x-2">
                <input wire:model="enableGroupMessages"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Enable Group Messages</span>
            </label>

            <label class="inline-flex items-center space-x-2">
                <input wire:model="enableStatusMessages"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Enable Status Updates</span>
            </label>

            <label class="inline-flex items-center space-x-2">
                <input wire:model="enableMediaDownload"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Auto Download Media</span>
            </label>

            <label class="inline-flex items-center space-x-2">
                <input wire:model="enableReadReceipts"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Send Read Receipts</span>
            </label>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row justify-between gap-4 pt-6 border-t border-slate-200 dark:border-navy-500">
        <div class="flex flex-wrap gap-2">
            @if($isConnected)
                <button wire:click="sendTestMessage"
                    class="btn border border-slate-300 text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                    <i class="fas fa-paper-plane mr-2"></i>
                    Send Test Message
                </button>
                <button wire:click="restartSession"
                    class="btn border border-warning text-warning hover:bg-warning/10 focus:bg-warning/10 active:bg-warning/20">
                    <i class="fas fa-redo mr-2"></i>
                    Restart Session
                </button>
                <button wire:click="disconnect"
                    class="btn border border-error text-error hover:bg-error/10 focus:bg-error/10 active:bg-error/20">
                    <i class="fas fa-sign-out-alt mr-2"></i>
                    Disconnect
                </button>
            @else
                <button wire:click="clearSession"
                    class="btn border border-slate-300 text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                    <i class="fas fa-trash mr-2"></i>
                    Clear Session Data
                </button>
            @endif
        </div>

        <div class="flex gap-2">
            <button wire:click="saveSettings"
                class="btn border border-slate-300 text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                <i class="fas fa-save mr-2"></i>
                Save Settings
            </button>
            @if(!$isConnected)
                <button wire:click="initializeConnection"
                    class="btn bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90">
                    <i class="fab fa-whatsapp mr-2"></i>
                    Initialize WhatsApp Web
                </button>
            @endif
        </div>
    </div>
</div>
